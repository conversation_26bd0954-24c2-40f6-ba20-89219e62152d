#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数输入框功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from all import DEFAULT_CONFIG, load_config, save_config
    print("成功导入配置相关函数")
    print("DEFAULT_CONFIG keys:", list(DEFAULT_CONFIG.keys()))
    print("digit_transition_weight value:", DEFAULT_CONFIG.get('digit_transition_weight', 'NOT FOUND'))
    
    # 验证权重总和
    weights = [
        DEFAULT_CONFIG['short_weight'],
        DEFAULT_CONFIG['mid_weight'], 
        DEFAULT_CONFIG['long_weight'],
        DEFAULT_CONFIG['co_weight'],
        DEFAULT_CONFIG['digit_transition_weight']
    ]
    total_weight = sum(weights)
    print(f"权重总和: {total_weight}")
    print(f"各权重: short={weights[0]}, mid={weights[1]}, long={weights[2]}, co={weights[3]}, digit_transition={weights[4]}")
    
    if abs(total_weight - 1.0) < 0.01:
        print("✓ 权重总和正确")
    else:
        print("✗ 权重总和不正确，应该接近1.0")
        
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def test_param_dialog():
    """测试参数对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建简化的参数对话框
    dialog = tk.Toplevel(root)
    dialog.title("参数测试")
    dialog.geometry("500x700")
    dialog.resizable(False, False)
    
    # 创建参数输入框
    param_entries = {}
    param_labels = {
        'alpha': '平滑因子 (alpha):',
        'lambda': '冷号衰减系数 (lambda):',
        'short_weight': '短期预测权重:',
        'long_weight': '长期预测权重:',
        'mid_weight': '中期预测权重:',
        'co_weight': '协同预测权重:',
        'digit_transition_weight': '数字转移权重:',
        'overall_weight': '全中率权重:',
        'hot_threshold': '热号阈值:',
        'cold_threshold': '冷号阈值:',
        'selection_count': '选择数量:',
        'window': '中期窗口大小:',
        'periodicity': '周期特征期数:',
        'hot_multiplier': '热号权重系数:',
        'cold_multiplier': '冷号权重系数:'
    }
    
    # 获取当前参数值
    current_params = DEFAULT_CONFIG.copy()
    
    frame = tk.Frame(dialog, padx=20, pady=10)
    frame.pack(fill=tk.BOTH, expand=True)
    
    for i, (param, label_text) in enumerate(param_labels.items()):
        tk.Label(frame, text=label_text, font=("SimHei", 10)).grid(row=i, column=0, sticky=tk.W, pady=5)
        
        if param in ['selection_count', 'window', 'periodicity']:
            var = tk.IntVar(value=int(current_params[param]))
        else:
            var = tk.DoubleVar(value=current_params[param])
        entry = tk.Entry(frame, textvariable=var, width=10)
        entry.grid(row=i, column=1, sticky=tk.W, pady=5)
        
        param_entries[param] = var
    
    # 添加说明文本
    help_text = "提示:\n" \
               "- 短期、中期、长期、协同和数字转移预测权重之和应为1\n" \
               "- 数字转移权重控制基于历史数字转移概率的预测影响\n" \
               "- 调整参数后需重新回测和预测\n" \
               "- 参数将自动保存到配置文件"
    tk.Label(frame, text=help_text, font=("SimHei", 9), fg="blue", justify=tk.LEFT).grid(
        row=len(param_labels), column=0, columnspan=2, sticky=tk.W, pady=10)
    
    # 按钮框架
    btn_frame = tk.Frame(dialog)
    btn_frame.pack(fill=tk.X, padx=20, pady=10)
    
    # 验证按钮
    def validate_params():
        new_params = {}
        for param, var in param_entries.items():
            try:
                if param == 'selection_count':
                    value = int(var.get())
                else:
                    value = float(var.get())
                new_params[param] = value
            except ValueError:
                messagebox.showerror("参数错误", f"{param_labels[param]}必须是数字")
                return
        
        # 验证权重之和是否接近1
        weights = [new_params['short_weight'], new_params['long_weight'], new_params['mid_weight'], 
                  new_params['co_weight'], new_params['digit_transition_weight']]
        weight_sum = sum(weights)
        
        if abs(weight_sum - 1.0) > 0.01:
            messagebox.showwarning("权重警告", f"五种预测方法的权重之和应接近1.0\n当前总和: {weight_sum:.3f}")
        else:
            messagebox.showinfo("验证成功", f"参数验证通过！\n权重总和: {weight_sum:.3f}")
    
    tk.Button(btn_frame, text="验证参数", command=validate_params, bg="#4CAF50", fg="white", 
             padx=15).pack(side=tk.LEFT, padx=5)
    
    tk.Button(btn_frame, text="关闭", command=dialog.destroy, bg="#f44336", fg="white", 
             padx=15).pack(side=tk.LEFT, padx=5)
    
    root.mainloop()

if __name__ == "__main__":
    print("启动参数对话框测试...")
    test_param_dialog()
