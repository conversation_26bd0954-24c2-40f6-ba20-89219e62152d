#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试配置
"""

# 默认配置
DEFAULT_CONFIG = {
    'alpha': 2.0,
    'lambda': 0.1,
    'short_weight': 0.15,
    'mid_weight': 0.25,
    'long_weight': 0.35,
    'co_weight': 0.15,
    'digit_transition_weight': 0.1,  # 新增：数字转移权重
    'overall_weight': 1.0,
    'hot_threshold': 1.5,
    'cold_threshold': 7.0,
    'hot_multiplier': 1.0,
    'cold_multiplier': 1.0,
    'selection_count': 2,
    'window': 30,
    'periodicity': 14
}

def test_config():
    print("测试配置...")
    print("DEFAULT_CONFIG keys:", list(DEFAULT_CONFIG.keys()))
    print("digit_transition_weight value:", DEFAULT_CONFIG.get('digit_transition_weight', 'NOT FOUND'))
    
    # 验证权重总和
    weights = [
        DEFAULT_CONFIG['short_weight'],
        DEFAULT_CONFIG['mid_weight'], 
        DEFAULT_CONFIG['long_weight'],
        DEFAULT_CONFIG['co_weight'],
        DEFAULT_CONFIG['digit_transition_weight']
    ]
    total_weight = sum(weights)
    print(f"权重总和: {total_weight}")
    print(f"各权重: short={weights[0]}, mid={weights[1]}, long={weights[2]}, co={weights[3]}, digit_transition={weights[4]}")
    
    if abs(total_weight - 1.0) < 0.01:
        print("✓ 权重总和正确")
        return True
    else:
        print("✗ 权重总和不正确，应该接近1.0")
        return False

def test_param_labels():
    """测试参数标签"""
    param_labels = {
        'alpha': '平滑因子 (alpha):',
        'lambda': '冷号衰减系数 (lambda):',
        'short_weight': '短期预测权重:',
        'long_weight': '长期预测权重:',
        'mid_weight': '中期预测权重:',
        'co_weight': '协同预测权重:',
        'digit_transition_weight': '数字转移权重:',
        'overall_weight': '全中率权重:',
        'hot_threshold': '热号阈值:',
        'cold_threshold': '冷号阈值:',
        'selection_count': '选择数量:',
        'window': '中期窗口大小:',
        'periodicity': '周期特征期数:',
        'hot_multiplier': '热号权重系数:',
        'cold_multiplier': '冷号权重系数:'
    }
    
    print("\n测试参数标签...")
    missing_params = []
    for param in param_labels.keys():
        if param not in DEFAULT_CONFIG:
            missing_params.append(param)
    
    if missing_params:
        print(f"✗ 缺少参数: {missing_params}")
        return False
    else:
        print("✓ 所有参数标签都在配置中")
        return True

if __name__ == "__main__":
    print("开始测试...")
    config_ok = test_config()
    labels_ok = test_param_labels()
    
    if config_ok and labels_ok:
        print("\n✓ 所有测试通过！数字转移权重参数已正确添加。")
    else:
        print("\n✗ 测试失败，需要修复配置。")
