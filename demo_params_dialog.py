#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示参数对话框，展示数字转移权重参数
"""

import tkinter as tk
from tkinter import messagebox

# 默认配置
DEFAULT_CONFIG = {
    'alpha': 2.0,
    'lambda': 0.1,
    'short_weight': 0.15,
    'mid_weight': 0.25,
    'long_weight': 0.35,
    'co_weight': 0.15,
    'digit_transition_weight': 0.1,  # 新增：数字转移权重
    'overall_weight': 1.0,
    'hot_threshold': 1.5,
    'cold_threshold': 7.0,
    'hot_multiplier': 1.0,
    'cold_multiplier': 1.0,
    'selection_count': 2,
    'window': 30,
    'periodicity': 14
}

def show_params_dialog():
    """显示参数设置对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = tk.Toplevel(root)
    dialog.title("模型参数设置 - 包含数字转移权重")
    dialog.geometry("600x700")
    dialog.resizable(False, False)
    
    # 创建参数输入框
    param_entries = {}
    param_labels = {
        'alpha': '平滑因子 (alpha):',
        'lambda': '冷号衰减系数 (lambda):',
        'short_weight': '短期预测权重:',
        'long_weight': '长期预测权重:',
        'mid_weight': '中期预测权重:',
        'co_weight': '协同预测权重:',
        'digit_transition_weight': '数字转移权重:',  # 新增的参数
        'overall_weight': '全中率权重:',
        'hot_threshold': '热号阈值:',
        'cold_threshold': '冷号阈值:',
        'selection_count': '选择数量:',
        'window': '中期窗口大小:',
        'periodicity': '周期特征期数:',
        'hot_multiplier': '热号权重系数:',
        'cold_multiplier': '冷号权重系数:'
    }
    
    # 获取当前参数值
    current_params = DEFAULT_CONFIG.copy()
    
    frame = tk.Frame(dialog, padx=20, pady=10)
    frame.pack(fill=tk.BOTH, expand=True)
    
    # 添加标题
    title_label = tk.Label(frame, text="模型参数设置", font=("SimHei", 14, "bold"), fg="blue")
    title_label.grid(row=0, column=0, columnspan=3, pady=(0, 15))
    
    for i, (param, label_text) in enumerate(param_labels.items(), 1):
        # 高亮显示新增的数字转移权重参数
        if param == 'digit_transition_weight':
            label = tk.Label(frame, text=label_text, font=("SimHei", 10, "bold"), fg="red")
            label.grid(row=i, column=0, sticky=tk.W, pady=5)
            
            # 添加说明
            note_label = tk.Label(frame, text="(新增)", font=("SimHei", 8), fg="red")
            note_label.grid(row=i, column=2, sticky=tk.W, padx=5)
        else:
            label = tk.Label(frame, text=label_text, font=("SimHei", 10))
            label.grid(row=i, column=0, sticky=tk.W, pady=5)
        
        if param in ['selection_count', 'window', 'periodicity']:
            var = tk.IntVar(value=int(current_params[param]))
        else:
            var = tk.DoubleVar(value=current_params[param])
        entry = tk.Entry(frame, textvariable=var, width=12)
        entry.grid(row=i, column=1, sticky=tk.W, pady=5, padx=10)
        
        param_entries[param] = var
    
    # 添加说明文本
    help_text = "提示:\n" \
               "• 短期、中期、长期、协同和数字转移预测权重之和应为1\n" \
               "• 数字转移权重控制基于历史数字转移概率的预测影响\n" \
               "• 数字转移分析当前数字到下一期数字的转移模式\n" \
               "• 调整参数后需重新回测和预测\n" \
               "• 参数将自动保存到配置文件"
    help_label = tk.Label(frame, text=help_text, font=("SimHei", 9), fg="blue", justify=tk.LEFT)
    help_label.grid(row=len(param_labels)+2, column=0, columnspan=3, sticky=tk.W, pady=15)
    
    # 按钮框架
    btn_frame = tk.Frame(dialog)
    btn_frame.pack(fill=tk.X, padx=20, pady=10)
    
    # 验证按钮
    def validate_params():
        new_params = {}
        for param, var in param_entries.items():
            try:
                if param in ['selection_count', 'window', 'periodicity']:
                    value = int(var.get())
                else:
                    value = float(var.get())
                new_params[param] = value
            except ValueError:
                messagebox.showerror("参数错误", f"{param_labels[param]}必须是数字")
                return
        
        # 验证权重之和是否接近1
        weights = [new_params['short_weight'], new_params['long_weight'], new_params['mid_weight'], 
                  new_params['co_weight'], new_params['digit_transition_weight']]
        weight_sum = sum(weights)
        
        result_text = f"参数验证结果:\n\n"
        result_text += f"权重分配:\n"
        result_text += f"• 短期预测权重: {weights[0]:.3f}\n"
        result_text += f"• 中期预测权重: {weights[1]:.3f}\n"
        result_text += f"• 长期预测权重: {weights[2]:.3f}\n"
        result_text += f"• 协同预测权重: {weights[3]:.3f}\n"
        result_text += f"• 数字转移权重: {weights[4]:.3f} (新增)\n"
        result_text += f"• 权重总和: {weight_sum:.3f}\n\n"
        
        if abs(weight_sum - 1.0) > 0.01:
            result_text += "⚠️ 警告: 五种预测方法的权重之和应接近1.0"
            messagebox.showwarning("权重警告", result_text)
        else:
            result_text += "✅ 验证通过: 权重配置正确！"
            messagebox.showinfo("验证成功", result_text)
    
    tk.Button(btn_frame, text="验证参数", command=validate_params, bg="#4CAF50", fg="white", 
             font=("SimHei", 10), padx=15).pack(side=tk.LEFT, padx=5)
    
    # 重置按钮
    def reset_params():
        for param, var in param_entries.items():
            var.set(DEFAULT_CONFIG[param])
        messagebox.showinfo("重置完成", "参数已重置为默认值")
    
    tk.Button(btn_frame, text="重置默认", command=reset_params, bg="#FF9800", fg="white", 
             font=("SimHei", 10), padx=15).pack(side=tk.LEFT, padx=5)
    
    tk.Button(btn_frame, text="关闭", command=dialog.destroy, bg="#f44336", fg="white", 
             font=("SimHei", 10), padx=15).pack(side=tk.LEFT, padx=5)
    
    # 显示当前权重总和
    weights = [DEFAULT_CONFIG['short_weight'], DEFAULT_CONFIG['mid_weight'], DEFAULT_CONFIG['long_weight'], 
              DEFAULT_CONFIG['co_weight'], DEFAULT_CONFIG['digit_transition_weight']]
    weight_sum = sum(weights)
    status_label = tk.Label(dialog, text=f"当前权重总和: {weight_sum:.3f}", 
                           font=("SimHei", 10), fg="green" if abs(weight_sum - 1.0) < 0.01 else "red")
    status_label.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    print("启动参数对话框演示...")
    print("展示新增的数字转移权重参数")
    show_params_dialog()
